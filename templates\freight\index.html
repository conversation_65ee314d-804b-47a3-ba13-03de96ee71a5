{% extends "base.html" %}

{% block title %}Bourse de Fret - SABTRANS{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <i class="fas fa-exchange-alt me-2"></i>Bourse de Fret
                    </h1>
                    <p class="text-muted">
                        {% if current_user.is_transporter() %}
                            Trouvez des chargements disponibles
                        {% elif current_user.is_shipper() %}
                            Trouvez des transporteurs disponibles
                        {% else %}
                            Offres de transport disponibles
                        {% endif %}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('freight.create') }}" class="btn btn-success">
                        <i class="fas fa-plus me-2"></i>Nouvelle Offre
                    </a>
                    <a href="{{ url_for('freight.my_offers') }}" class="btn btn-outline-primary">
                        <i class="fas fa-list me-2"></i>Mes Offres
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres de recherche -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>Filtres de Recherche
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('freight.index') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="search" class="form-label">Recherche</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query }}" placeholder="Titre, description...">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="goods_type" class="form-label">Type de marchandise</label>
                        <select class="form-select" id="goods_type" name="goods_type">
                            <option value="">Tous types</option>
                            {% for goods in goods_types %}
                                <option value="{{ goods }}" {% if filters.goods_type == goods %}selected{% endif %}>
                                    {{ goods }}
                                </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="pickup_city" class="form-label">Ville de collecte</label>
                        <input type="text" class="form-control" id="pickup_city" name="pickup_city" 
                               value="{{ filters.pickup_city }}" placeholder="Paris, Lyon...">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="delivery_city" class="form-label">Ville de livraison</label>
                        <input type="text" class="form-control" id="delivery_city" name="delivery_city" 
                               value="{{ filters.delivery_city }}" placeholder="Marseille, Lille...">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="date_from" class="form-label">Date de</label>
                        <input type="date" class="form-control" id="date_from" name="date_from"
                               value="{{ filters.date_from }}">
                    </div>
                    <div class="col-md-1 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>

                <!-- Deuxième ligne : Type d'offre et tri -->
                <div class="row">
                    <div class="col-md-2 mb-3">
                        <label for="offer_type" class="form-label">Type d'offre</label>
                        <select class="form-select" id="offer_type" name="offer_type">
                            <option value="">Tous types</option>
                            <option value="demande" {% if filters.offer_type == 'demande' %}selected{% endif %}>Demandes</option>
                            <option value="offre" {% if filters.offer_type == 'offre' %}selected{% endif %}>Offres</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="sort_by" class="form-label">Trier par</label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="created_at" {% if filters.sort_by == 'created_at' %}selected{% endif %}>Date de création</option>
                            <option value="pickup_date" {% if filters.sort_by == 'pickup_date' %}selected{% endif %}>Date de collecte</option>
                            <option value="price" {% if filters.sort_by == 'price' %}selected{% endif %}>Prix</option>
                            <option value="distance" {% if filters.sort_by == 'distance' %}selected{% endif %}>Distance</option>
                            {% if filters.offer_type == 'demande' %}
                            <option value="best_price" {% if filters.sort_by == 'best_price' %}selected{% endif %}>Meilleur prix proposé</option>
                            {% endif %}
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="order" class="form-label">Ordre</label>
                        <select class="form-select" id="order" name="order">
                            <option value="desc" {% if filters.order == 'desc' %}selected{% endif %}>Décroissant</option>
                            <option value="asc" {% if filters.order == 'asc' %}selected{% endif %}>Croissant</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3 d-flex align-items-end">
                        <div class="alert alert-info mb-0 w-100">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>Astuce :</strong> Pour les demandes, utilisez "Meilleur prix proposé" pour voir les offres avec les propositions les moins chères en premier.
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Résultats -->
    <div class="row">
        <div class="col-12">
            {% if offers.items %}
                <!-- Statistiques -->
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <p class="text-muted mb-0">
                        {{ offers.total }} offre(s) trouvée(s) - Page {{ offers.page }} sur {{ offers.pages }}
                    </p>
                    <div class="btn-group btn-group-sm" role="group">
                        <input type="radio" class="btn-check" name="view" id="view-list" checked>
                        <label class="btn btn-outline-primary" for="view-list">
                            <i class="fas fa-list"></i>
                        </label>
                        <input type="radio" class="btn-check" name="view" id="view-grid">
                        <label class="btn btn-outline-primary" for="view-grid">
                            <i class="fas fa-th"></i>
                        </label>
                    </div>
                </div>

                <!-- Liste des offres -->
                <div id="offers-list">
                    {% for offer in offers.items %}
                    <div class="card freight-card mb-3 {% if offer.is_urgent %}urgent{% endif %}">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-8">
                                    <div class="d-flex justify-content-between align-items-start mb-2">
                                        <h5 class="card-title mb-1">
                                            {{ offer.title }}
                                            {% if offer.is_urgent %}
                                                <span class="badge bg-danger ms-2">URGENT</span>
                                            {% endif %}
                                            {% if offer.is_private %}
                                                <span class="badge bg-warning ms-2">PRIVÉ</span>
                                            {% endif %}
                                        </h5>
                                        <small class="text-muted">
                                            {% if offer.created_at %}
                                                {{ offer.created_at.strftime('%d/%m/%Y %H:%M') }}
                                            {% else %}
                                                Date inconnue
                                            {% endif %}
                                        </small>
                                    </div>
                                    
                                    <div class="row mb-2">
                                        <div class="col-md-6">
                                            <p class="mb-1">
                                                <i class="fas fa-box text-primary me-2"></i>
                                                <strong>{{ offer.goods_type }}</strong>
                                                {% if offer.weight %}
                                                    - {{ offer.weight }}t
                                                {% endif %}
                                                {% if offer.volume %}
                                                    - {{ offer.volume }}m³
                                                {% endif %}
                                            </p>
                                        </div>
                                        <div class="col-md-6">
                                            <p class="mb-1">
                                                <i class="fas fa-calendar text-success me-2"></i>
                                                {% if offer.pickup_date %}
                                                    {{ offer.pickup_date.strftime('%d/%m/%Y') }}
                                                {% else %}
                                                    <span class="text-muted">Date non définie</span>
                                                {% endif %}
                                                {% if offer.flexible_dates %}
                                                    <span class="badge bg-info ms-1">Flexible</span>
                                                {% endif %}
                                            </p>
                                        </div>
                                    </div>
                                    
                                    <div class="route-info mb-2">
                                        <i class="fas fa-route text-warning me-2"></i>
                                        <span class="fw-bold">{{ offer.get_route_summary() }}</span>
                                        {% if offer.distance_km %}
                                            <small class="text-muted ms-2">({{ offer.distance_km }} km)</small>
                                        {% endif %}
                                    </div>
                                    
                                    {% if offer.description %}
                                    <p class="text-muted small mb-2">{{ offer.description[:100] }}...</p>
                                    {% endif %}
                                    
                                    <div class="d-flex flex-wrap gap-1">
                                        <span class="badge bg-secondary">{{ offer.offer_type.title() }}</span>
                                        {% if offer.vehicle_type %}
                                            <span class="badge bg-info">{{ offer.vehicle_type }}</span>
                                        {% endif %}
                                        {% if offer.packaging %}
                                            <span class="badge bg-light text-dark">{{ offer.packaging }}</span>
                                        {% endif %}
                                    </div>
                                </div>
                                
                                <div class="col-md-4 text-end">
                                    {% if offer.price %}
                                    <div class="price-info mb-3">
                                        <h4 class="text-success mb-0">{{ "%.0f"|format(offer.price) }}€</h4>
                                        <small class="text-muted">{{ offer.price_type }}</small>
                                    </div>
                                    {% endif %}

                                    {% if offer.offer_type == 'demande' %}
                                    <!-- Affichage du nombre de propositions et du meilleur prix -->
                                    {% set proposal_count = offer.proposals|length %}
                                    {% if proposal_count > 0 %}
                                    <div class="proposals-info mb-3">
                                        <div class="badge bg-primary mb-1">
                                            {{ proposal_count }} proposition{{ 's' if proposal_count > 1 else '' }}
                                        </div>
                                        {% set best_proposal = offer.proposals|selectattr('status', 'equalto', 'pending')|list|sort(attribute='proposed_price')|first %}
                                        {% if best_proposal %}
                                        <div class="best-price">
                                            <h5 class="text-warning mb-0">
                                                <i class="fas fa-star me-1"></i>{{ "%.0f"|format(best_proposal.proposed_price) }}€
                                            </h5>
                                            <small class="text-muted">Meilleur prix</small>
                                        </div>
                                        {% endif %}
                                    </div>
                                    {% else %}
                                    <div class="no-proposals mb-3">
                                        <small class="text-muted">Aucune proposition</small>
                                    </div>
                                    {% endif %}
                                    {% endif %}
                                    
                                    <div class="d-grid gap-2">
                                        <a href="{{ url_for('freight.detail', offer_id=offer.id) }}" 
                                           class="btn btn-primary">
                                            <i class="fas fa-eye me-2"></i>Voir détails
                                        </a>
                                        {% if current_user.id != offer.user_id %}
                                        <button class="btn btn-success btn-sm" 
                                                onclick="contactOffer({{ offer.id }})">
                                            <i class="fas fa-envelope me-2"></i>Contacter
                                        </button>
                                        {% endif %}
                                    </div>
                                    
                                    <small class="text-muted d-block mt-2">
                                        Par {{ offer.user.company_name or offer.user.get_full_name() }}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>

                <!-- Pagination -->
                {% if offers.pages > 1 %}
                <nav aria-label="Navigation des offres">
                    <ul class="pagination justify-content-center">
                        {% if offers.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('freight.index', page=offers.prev_num, **filters) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in offers.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != offers.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('freight.index', page=page_num, **filters) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if offers.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('freight.index', page=offers.next_num, **filters) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <!-- Aucun résultat -->
                <div class="text-center py-5">
                    <i class="fas fa-search fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucune offre trouvée</h4>
                    <p class="text-muted">
                        Essayez de modifier vos critères de recherche ou 
                        <a href="{{ url_for('freight.create') }}">créez une nouvelle offre</a>.
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function contactOffer(offerId) {
    // Ici on pourrait implémenter un système de messagerie
    alert('Fonctionnalité de contact en cours de développement');
}

// Sauvegarde automatique des filtres
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('input, select');
    
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            // Auto-submit après un délai
            setTimeout(() => {
                form.submit();
            }, 500);
        });
    });
});
</script>
{% endblock %}
