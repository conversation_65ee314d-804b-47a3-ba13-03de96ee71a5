{% extends "base.html" %}

{% block title %}Dashboard Transporteur - SABTRANS{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 text-primary">
                <i class="fas fa-truck me-2"></i>Dashboard Transporteur
            </h1>
            <p class="text-muted">Bienvenue {{ current_user.get_full_name() }}</p>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Missions Totales</h6>
                            <h2 class="stat-number">{{ total_missions }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-clipboard-list fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Missions Actives</h6>
                            <h2 class="stat-number text-success">{{ active_missions }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shipping-fast fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card info h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Revenus Totaux</h6>
                            <h2 class="stat-number text-info">{{ "%.0f"|format(total_revenue) }}€</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-euro-sign fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card warning h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Propositions Envoyées</h6>
                            <h2 class="stat-number text-warning">{{ pending_proposals }}</h2>
                            <small class="text-muted">En attente</small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-paper-plane fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section Mes Propositions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-paper-plane me-2"></i>Mes Propositions de Prix
                        {% if pending_proposals > 0 %}
                            <span class="badge bg-danger ms-2">{{ pending_proposals }}</span>
                        {% endif %}
                    </h5>
                </div>
                <div class="card-body">
                    {% if my_proposals %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Demande</th>
                                        <th>Expéditeur</th>
                                        <th>Itinéraire</th>
                                        <th>Mon Prix</th>
                                        <th>Statut</th>
                                        <th>Date Envoi</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for proposal in my_proposals %}
                                    <tr class="{{ 'table-warning' if proposal.status == 'pending' else 'table-success' if proposal.status == 'accepted' else 'table-danger' if proposal.status == 'rejected' else '' }}">
                                        <td>
                                            <strong>{{ proposal.freight_offer.title }}</strong><br>
                                            <small class="text-muted">{{ proposal.freight_offer.goods_type }}</small>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="avatar-sm bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2">
                                                    {{ proposal.freight_offer.user.first_name[0] }}{{ proposal.freight_offer.user.last_name[0] }}
                                                </div>
                                                <div>
                                                    <strong>{{ proposal.freight_offer.user.get_full_name() }}</strong><br>
                                                    {% if proposal.freight_offer.user.company_name %}
                                                        <small class="text-muted">{{ proposal.freight_offer.user.company_name }}</small>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <small>
                                                <i class="fas fa-map-marker-alt text-success me-1"></i>
                                                {{ proposal.freight_offer.pickup_city }}<br>
                                                <i class="fas fa-flag-checkered text-danger me-1"></i>
                                                {{ proposal.freight_offer.delivery_city }}
                                                {% if proposal.freight_offer.distance_km %}
                                                    <br><small class="text-muted">({{ proposal.freight_offer.distance_km }} km)</small>
                                                {% endif %}
                                            </small>
                                        </td>
                                        <td>
                                            <h5 class="text-success mb-0">{{ "%.0f"|format(proposal.proposed_price) }}€</h5>
                                            <small class="text-muted">{{ proposal.price_type or 'Prix fixe' }}</small>
                                            {% if proposal.freight_offer.distance_km %}
                                                <br><small class="text-muted">{{ "%.2f"|format(proposal.proposed_price / proposal.freight_offer.distance_km) }}€/km</small>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'warning' if proposal.status == 'pending' else 'success' if proposal.status == 'accepted' else 'danger' }}">
                                                {% if proposal.status %}
                                                    {% if proposal.status == 'pending' %}
                                                        En attente
                                                    {% elif proposal.status == 'accepted' %}
                                                        Acceptée
                                                    {% elif proposal.status == 'rejected' %}
                                                        Rejetée
                                                    {% else %}
                                                        {{ proposal.status.title() }}
                                                    {% endif %}
                                                {% else %}
                                                    Statut non défini
                                                {% endif %}
                                            </span>
                                        </td>
                                        <td>
                                            <small>
                                                {% if proposal.created_at %}
                                                    {{ proposal.created_at.strftime('%d/%m/%Y %H:%M') }}
                                                {% else %}
                                                    Date inconnue
                                                {% endif %}
                                            </small>
                                        </td>
                                        <td>
                                            <a href="{{ url_for('freight.detail', id=proposal.freight_offer.id) }}" class="btn btn-sm btn-outline-primary" title="Voir détails">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        {% if my_proposals|length >= 10 %}
                        <div class="text-center mt-3">
                            <a href="{{ url_for('freight.index') }}" class="btn btn-outline-warning">
                                <i class="fas fa-eye me-2"></i>Voir toutes mes propositions
                            </a>
                        </div>
                        {% endif %}
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-paper-plane fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune proposition envoyée</p>
                            <p class="text-muted">Consultez les demandes de transport disponibles et proposez vos prix.</p>
                            <a href="{{ url_for('freight.index') }}" class="btn btn-warning">
                                <i class="fas fa-search me-2"></i>Rechercher des demandes
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Missions en cours -->
        <div class="col-lg-8 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tasks me-2"></i>Missions en Cours
                    </h5>
                </div>
                <div class="card-body">
                    {% if current_missions %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Mission</th>
                                        <th>Itinéraire</th>
                                        <th>Collecte</th>
                                        <th>Statut</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for mission in current_missions %}
                                    <tr>
                                        <td>
                                            <strong>{{ mission.mission_number }}</strong><br>
                                            <small class="text-muted">{{ mission.title[:50] }}...</small>
                                        </td>
                                        <td>
                                            <i class="fas fa-map-marker-alt text-success me-1"></i>
                                            {{ mission.freight_offer.pickup_city }}<br>
                                            <i class="fas fa-flag-checkered text-danger me-1"></i>
                                            {{ mission.freight_offer.delivery_city }}
                                        </td>
                                        <td>
                                            {% if mission.pickup_scheduled_at %}
                                                {{ mission.pickup_scheduled_at.strftime('%d/%m/%Y %H:%M') }}
                                            {% else %}
                                                <span class="text-muted">Non programmée</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if mission.status == 'in_transit' else 'warning' }}">
                                                {{ mission.get_status_label() }}
                                            </span>
                                        </td>
                                        <td>
                                            <a href="#" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune mission en cours</p>
                            <a href="{{ url_for('freight.index') }}" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Rechercher des offres
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Nouvelles offres disponibles -->
        <div class="col-lg-4 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>Nouvelles Offres
                    </h5>
                </div>
                <div class="card-body">
                    {% if available_offers %}
                        {% for offer in available_offers[:5] %}
                        <div class="freight-card border-start border-3 p-3 mb-3 bg-light">
                            <h6 class="mb-1">{{ offer.title }}</h6>
                            <p class="text-muted small mb-2">
                                <i class="fas fa-route me-1"></i>
                                {{ offer.get_route_summary() }}
                            </p>
                            <div class="d-flex justify-content-between align-items-center">
                                <span class="badge bg-primary">{{ offer.goods_type }}</span>
                                {% if offer.price %}
                                    <strong class="text-success">{{ "%.0f"|format(offer.price) }}€</strong>
                                {% endif %}
                            </div>
                            <div class="mt-2">
                                <a href="{{ url_for('freight.detail', offer_id=offer.id) }}" class="btn btn-sm btn-outline-primary">
                                    Voir détails
                                </a>
                            </div>
                        </div>
                        {% endfor %}
                        
                        <div class="text-center">
                            <a href="{{ url_for('freight.index') }}" class="btn btn-success">
                                <i class="fas fa-eye me-2"></i>Voir toutes les offres
                            </a>
                        </div>
                    {% else %}
                        <div class="text-center py-3">
                            <i class="fas fa-search fa-2x text-muted mb-2"></i>
                            <p class="text-muted">Aucune nouvelle offre</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Missions récentes -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Missions Récentes
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_missions %}
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Mission</th>
                                        <th>Expéditeur</th>
                                        <th>Itinéraire</th>
                                        <th>Date</th>
                                        <th>Statut</th>
                                        <th>Montant</th>
                                        <th>Note</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for mission in recent_missions %}
                                    <tr>
                                        <td>
                                            <strong>{{ mission.mission_number }}</strong><br>
                                            <small class="text-muted">{{ mission.title[:30] }}...</small>
                                        </td>
                                        <td>{{ mission.shipper.company_name or mission.shipper.get_full_name() }}</td>
                                        <td>
                                            <small>
                                                {{ mission.freight_offer.pickup_city }} → 
                                                {{ mission.freight_offer.delivery_city }}
                                            </small>
                                        </td>
                                        <td>
                                            {% if mission.created_at %}
                                                {{ mission.created_at.strftime('%d/%m/%Y') }}
                                            {% else %}
                                                <span class="text-muted">Date inconnue</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 'success' if mission.status == 'completed' else 'secondary' }}">
                                                {{ mission.get_status_label() }}
                                            </span>
                                        </td>
                                        <td>
                                            {% if mission.agreed_price %}
                                                {{ "%.0f"|format(mission.agreed_price) }}€
                                            {% else %}
                                                <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            {% if mission.transporter_rating %}
                                                {% for i in range(1, 6) %}
                                                    {% if i <= mission.transporter_rating %}
                                                        <i class="fas fa-star text-warning"></i>
                                                    {% else %}
                                                        <i class="far fa-star text-muted"></i>
                                                    {% endif %}
                                                {% endfor %}
                                            {% else %}
                                                <span class="text-muted">Non évalué</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-3x text-muted mb-3"></i>
                            <p class="text-muted">Aucune mission récente</p>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Actualisation automatique des données
setInterval(function() {
    // Ici on pourrait ajouter une actualisation AJAX des données
    console.log('Actualisation des données du dashboard...');
}, 60000); // Toutes les minutes
</script>
{% endblock %}
