{% extends "base.html" %}

{% block title %}Dashboard Administrateur - SABTRANS{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <h1 class="h3 text-primary">
                <i class="fas fa-cogs me-2"></i>Dashboard Administrateur
            </h1>
            <p class="text-muted">Vue d'ensemble de la plateforme SABTRANS</p>
        </div>
    </div>

    <!-- Statistiques principales -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Utilisateurs Totaux</h6>
                            <h2 class="stat-number">{{ total_users }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Offres Totales</h6>
                            <h2 class="stat-number text-success">{{ total_offers }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-exchange-alt fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card warning h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Missions Totales</h6>
                            <h2 class="stat-number text-warning">{{ total_missions }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-tasks fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card info h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Missions Actives</h6>
                            <h2 class="stat-number text-info">{{ active_missions }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-shipping-fast fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <!-- Répartition des utilisateurs -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-pie me-2"></i>Répartition des Utilisateurs
                    </h5>
                </div>
                <div class="card-body">
                    {% if user_stats %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Type</th>
                                        <th>Nombre</th>
                                        <th>Pourcentage</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stat in user_stats %}
                                    <tr>
                                        <td>
                                            <span class="badge bg-secondary">{{ stat.user_type.title() }}</span>
                                        </td>
                                        <td><strong>{{ stat.count }}</strong></td>
                                        <td>
                                            {% set percentage = (stat.count / total_users * 100) if total_users > 0 else 0 %}
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar" role="progressbar" 
                                                     style="width: {{ percentage }}%">
                                                    {{ "%.1f"|format(percentage) }}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">Aucune donnée disponible</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Statut des missions -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Statut des Missions
                    </h5>
                </div>
                <div class="card-body">
                    {% if mission_stats %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Statut</th>
                                        <th>Nombre</th>
                                        <th>Répartition</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for stat in mission_stats %}
                                    <tr>
                                        <td>
                                            <span class="badge bg-{{ 'success' if stat.status == 'completed' else 'primary' }}">
                                                {{ stat.status.title() }}
                                            </span>
                                        </td>
                                        <td><strong>{{ stat.count }}</strong></td>
                                        <td>
                                            {% set percentage = (stat.count / total_missions * 100) if total_missions > 0 else 0 %}
                                            <div class="progress" style="height: 20px;">
                                                <div class="progress-bar bg-{{ 'success' if stat.status == 'completed' else 'primary' }}" 
                                                     role="progressbar" style="width: {{ percentage }}%">
                                                    {{ "%.1f"|format(percentage) }}%
                                                </div>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">Aucune mission trouvée</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Offres récentes -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0">
                        <i class="fas fa-clock me-2"></i>Offres Récentes
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_offers %}
                        <div class="list-group list-group-flush">
                            {% for offer in recent_offers %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">{{ offer.title }}</div>
                                    <small class="text-muted">
                                        {{ offer.pickup_city }} → {{ offer.delivery_city }}
                                    </small>
                                    <br>
                                    <small class="text-muted">
                                        Par {{ offer.user.company_name or offer.user.get_full_name() }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-{{ 'primary' if offer.offer_type == 'demande' else 'success' }}">
                                        {% if offer.offer_type %}
                                            {{ offer.offer_type.title() }}
                                        {% else %}
                                            Type non défini
                                        {% endif %}
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        {% if offer.created_at %}
                                            {{ offer.created_at.strftime('%d/%m %H:%M') }}
                                        {% else %}
                                            Date inconnue
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ url_for('freight.index') }}" class="btn btn-outline-primary btn-sm">
                                Voir toutes les offres
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">Aucune offre récente</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Missions récentes -->
        <div class="col-lg-6 mb-4">
            <div class="card">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-history me-2"></i>Missions Récentes
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_missions %}
                        <div class="list-group list-group-flush">
                            {% for mission in recent_missions %}
                            <div class="list-group-item d-flex justify-content-between align-items-start">
                                <div class="ms-2 me-auto">
                                    <div class="fw-bold">{{ mission.mission_number }}</div>
                                    <small class="text-muted">{{ mission.title }}</small>
                                    <br>
                                    <small class="text-muted">
                                        Transporteur: {{ mission.transporter.company_name or mission.transporter.get_full_name() }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-{{ 'success' if mission.status == 'completed' else 'warning' }}">
                                        {% if mission.status %}
                                            {{ mission.status.title() }}
                                        {% else %}
                                            Statut non défini
                                        {% endif %}
                                    </span>
                                    <br>
                                    <small class="text-muted">
                                        {% if mission.created_at %}
                                            {{ mission.created_at.strftime('%d/%m %H:%M') }}
                                        {% else %}
                                            Date inconnue
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                        <div class="text-center mt-3">
                            <a href="#" class="btn btn-outline-info btn-sm">
                                Voir toutes les missions
                            </a>
                        </div>
                    {% else %}
                        <p class="text-muted text-center">Aucune mission récente</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-tools me-2"></i>Actions Rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="{{ url_for('admin.users') }}" class="btn btn-outline-primary">
                                    <i class="fas fa-users me-2"></i>Gérer les Utilisateurs
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="{{ url_for('freight.index') }}" class="btn btn-outline-success">
                                    <i class="fas fa-exchange-alt me-2"></i>Voir les Offres
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="#" class="btn btn-outline-warning">
                                    <i class="fas fa-chart-line me-2"></i>Statistiques Avancées
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="#" class="btn btn-outline-info">
                                    <i class="fas fa-cog me-2"></i>Configuration
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Actualisation automatique des statistiques
setInterval(function() {
    fetch('/dashboard/stats/api')
        .then(response => response.json())
        .then(data => {
            console.log('Statistiques mises à jour:', data);
            // Ici on pourrait mettre à jour les graphiques en temps réel
        })
        .catch(error => console.error('Erreur lors de la mise à jour:', error));
}, 60000); // Toutes les minutes
</script>
{% endblock %}
