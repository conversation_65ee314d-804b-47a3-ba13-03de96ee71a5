{% extends "base.html" %}

{% block title %}Gestion des Utilisateurs - SABTRANS{% endblock %}

{# Initialisation des variables par défaut si non définies #}
{% set filters = filters or {} %}
{% set search_query = search_query or '' %}
{% set total_users = total_users or 0 %}
{% set active_users = active_users or 0 %}
{% set pending_users = pending_users or 0 %}
{% set new_users_week = new_users_week or 0 %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 text-primary">
                        <i class="fas fa-users me-2"></i>Gestion des Utilisateurs
                    </h1>
                    <p class="text-muted">Administration des comptes utilisateurs</p>
                </div>
                <div>
                    <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#addUserModal">
                        <i class="fas fa-user-plus me-2"></i>Nouvel Utilisateur
                    </button>
                    <a href="{{ url_for('dashboard.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour Dashboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques rapides -->
    <div class="row mb-4">
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Total Utilisateurs</h6>
                            <h2 class="stat-number">{{ total_users }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card success h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Utilisateurs Actifs</h6>
                            <h2 class="stat-number text-success">{{ active_users }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-check fa-2x text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card warning h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">En Attente</h6>
                            <h2 class="stat-number text-warning">{{ pending_users }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-clock fa-2x text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-lg-3 col-md-6 mb-3">
            <div class="card dashboard-card info h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title text-muted">Nouveaux (7j)</h6>
                            <h2 class="stat-number text-info">{{ new_users_week }}</h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-user-plus fa-2x text-info"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtres et recherche -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="fas fa-filter me-2"></i>Filtres et Recherche
            </h5>
        </div>
        <div class="card-body">
            <form method="GET" action="{{ url_for('admin.users') }}">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="search" class="form-label">Recherche</label>
                        <input type="text" class="form-control" id="search" name="search" 
                               value="{{ search_query }}" placeholder="Nom, email, entreprise...">
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="user_type" class="form-label">Type</label>
                        <select class="form-select" id="user_type" name="user_type">
                            <option value="">Tous types</option>
                            <option value="admin" {% if filters.get('user_type') == 'admin' %}selected{% endif %}>Admin</option>
                            <option value="transporteur" {% if filters.get('user_type') == 'transporteur' %}selected{% endif %}>Transporteur</option>
                            <option value="expediteur" {% if filters.get('user_type') == 'expediteur' %}selected{% endif %}>Expéditeur</option>
                            <option value="chauffeur" {% if filters.get('user_type') == 'chauffeur' %}selected{% endif %}>Chauffeur</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="status" class="form-label">Statut</label>
                        <select class="form-select" id="status" name="status">
                            <option value="">Tous statuts</option>
                            <option value="active" {% if filters.get('status') == 'active' %}selected{% endif %}>Actif</option>
                            <option value="inactive" {% if filters.get('status') == 'inactive' %}selected{% endif %}>Inactif</option>
                            <option value="verified" {% if filters.get('status') == 'verified' %}selected{% endif %}>Vérifié</option>
                            <option value="unverified" {% if filters.get('status') == 'unverified' %}selected{% endif %}>Non vérifié</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="sort_by" class="form-label">Trier par</label>
                        <select class="form-select" id="sort_by" name="sort_by">
                            <option value="created_at" {% if filters.get('sort_by') == 'created_at' %}selected{% endif %}>Date création</option>
                            <option value="last_login" {% if filters.get('sort_by') == 'last_login' %}selected{% endif %}>Dernière connexion</option>
                            <option value="username" {% if filters.get('sort_by') == 'username' %}selected{% endif %}>Nom d'utilisateur</option>
                            <option value="company_name" {% if filters.get('sort_by') == 'company_name' %}selected{% endif %}>Entreprise</option>
                        </select>
                    </div>
                    <div class="col-md-2 mb-3">
                        <label for="order" class="form-label">Ordre</label>
                        <select class="form-select" id="order" name="order">
                            <option value="desc" {% if filters.get('order') == 'desc' %}selected{% endif %}>Décroissant</option>
                            <option value="asc" {% if filters.get('order') == 'asc' %}selected{% endif %}>Croissant</option>
                        </select>
                    </div>
                    <div class="col-md-1 mb-3 d-flex align-items-end">
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Liste des utilisateurs -->
    <div class="card">
        <div class="card-header bg-primary text-white">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Liste des Utilisateurs
                </h5>
                <div class="btn-group btn-group-sm" role="group">
                    <input type="radio" class="btn-check" name="view" id="view-table" checked>
                    <label class="btn btn-outline-light" for="view-table">
                        <i class="fas fa-table"></i>
                    </label>
                    <input type="radio" class="btn-check" name="view" id="view-cards">
                    <label class="btn btn-outline-light" for="view-cards">
                        <i class="fas fa-th"></i>
                    </label>
                </div>
            </div>
        </div>
        <div class="card-body">
            {% if users.items %}
                <!-- Vue tableau -->
                <div id="table-view">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Utilisateur</th>
                                    <th>Type</th>
                                    <th>Entreprise</th>
                                    <th>Email</th>
                                    <th>Statut</th>
                                    <th>Dernière connexion</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users.items %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-circle me-3">
                                                {{ user.first_name[0] }}{{ user.last_name[0] }}
                                            </div>
                                            <div>
                                                <strong>{{ user.get_full_name() }}</strong><br>
                                                <small class="text-muted">@{{ user.username }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="badge bg-{{ 'primary' if user.user_type == 'admin' else 'secondary' }}">
                                            {% if user.user_type %}
                                                {{ user.user_type.title() }}
                                            {% else %}
                                                Type non défini
                                            {% endif %}
                                        </span>
                                    </td>
                                    <td>
                                        {% if user.company_name %}
                                            {{ user.company_name }}<br>
                                            {% if user.city %}
                                                <small class="text-muted">{{ user.city }}</small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {{ user.email }}
                                        {% if user.email_confirmed %}
                                            <i class="fas fa-check-circle text-success ms-1" title="Email confirmé"></i>
                                        {% else %}
                                            <i class="fas fa-exclamation-circle text-warning ms-1" title="Email non confirmé"></i>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">Actif</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactif</span>
                                        {% endif %}
                                        {% if user.is_verified %}
                                            <span class="badge bg-info ms-1">Vérifié</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.last_login %}
                                            {{ user.last_login.strftime('%d/%m/%Y %H:%M') }}
                                        {% else %}
                                            <span class="text-muted">Jamais</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="btn-group btn-group-sm" role="group">
                                            <a href="{{ url_for('admin.user_detail', user_id=user.id) }}" class="btn btn-outline-primary" title="Voir">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="{{ url_for('admin.edit_user', user_id=user.id) }}" class="btn btn-outline-warning" title="Modifier">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if user.is_active %}
                                                <button class="btn btn-outline-danger" onclick="toggleUserStatus({{ user.id }}, false)" title="Désactiver">
                                                    <i class="fas fa-user-slash"></i>
                                                </button>
                                            {% else %}
                                                <button class="btn btn-outline-success" onclick="toggleUserStatus({{ user.id }}, true)" title="Activer">
                                                    <i class="fas fa-user-check"></i>
                                                </button>
                                            {% endif %}
                                            {% if not user.is_verified %}
                                                <button class="btn btn-outline-info" onclick="verifyUser({{ user.id }})" title="Vérifier">
                                                    <i class="fas fa-certificate"></i>
                                                </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>

                <!-- Vue cartes (cachée par défaut) -->
                <div id="cards-view" style="display: none;">
                    <div class="row">
                        {% for user in users.items %}
                        <div class="col-lg-4 col-md-6 mb-4">
                            <div class="card user-card h-100">
                                <div class="card-body">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="avatar-circle me-3">
                                            {{ user.first_name[0] }}{{ user.last_name[0] }}
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ user.get_full_name() }}</h6>
                                            <small class="text-muted">@{{ user.username }}</small>
                                        </div>
                                    </div>
                                    
                                    <div class="mb-2">
                                        <span class="badge bg-{{ 'primary' if user.user_type == 'admin' else 'secondary' }}">
                                            {% if user.user_type %}
                                                {{ user.user_type.title() }}
                                            {% else %}
                                                Type non défini
                                            {% endif %}
                                        </span>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">Actif</span>
                                        {% else %}
                                            <span class="badge bg-danger">Inactif</span>
                                        {% endif %}
                                    </div>
                                    
                                    <p class="text-muted small mb-2">{{ user.email }}</p>
                                    
                                    {% if user.company_name %}
                                        <p class="text-muted small mb-2">
                                            <i class="fas fa-building me-1"></i>{{ user.company_name }}
                                        </p>
                                    {% endif %}
                                    
                                    <p class="text-muted small mb-3">
                                        <i class="fas fa-calendar me-1"></i>
                                        Membre depuis {{ user.created_at.strftime('%m/%Y') }}
                                    </p>
                                    
                                    <div class="d-grid gap-1">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button class="btn btn-outline-primary" onclick="viewUser({{ user.id }})">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                            <button class="btn btn-outline-warning" onclick="editUser({{ user.id }})">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-{{ 'danger' if user.is_active else 'success' }}" 
                                                    onclick="toggleUserStatus({{ user.id }}, {{ 'false' if user.is_active else 'true' }})">
                                                <i class="fas fa-user-{{ 'slash' if user.is_active else 'check' }}"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div>

                <!-- Pagination -->
                {% if users.pages > 1 %}
                <nav aria-label="Navigation des utilisateurs" class="mt-4">
                    <ul class="pagination justify-content-center">
                        {% if users.has_prev %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.users', page=users.prev_num, search=search_query, user_type=filters.get('user_type', ''), status=filters.get('status', ''), sort_by=filters.get('sort_by', ''), order=filters.get('order', '')) }}">
                                    <i class="fas fa-chevron-left"></i>
                                </a>
                            </li>
                        {% endif %}
                        
                        {% for page_num in users.iter_pages() %}
                            {% if page_num %}
                                {% if page_num != users.page %}
                                    <li class="page-item">
                                        <a class="page-link" href="{{ url_for('admin.users', page=page_num, search=search_query, user_type=filters.get('user_type', ''), status=filters.get('status', ''), sort_by=filters.get('sort_by', ''), order=filters.get('order', '')) }}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% else %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if users.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="{{ url_for('admin.users', page=users.next_num, search=search_query, user_type=filters.get('user_type', ''), status=filters.get('status', ''), sort_by=filters.get('sort_by', ''), order=filters.get('order', '')) }}">
                                    <i class="fas fa-chevron-right"></i>
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </nav>
                {% endif %}

            {% else %}
                <!-- Aucun utilisateur -->
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h4 class="text-muted">Aucun utilisateur trouvé</h4>
                    <p class="text-muted">
                        {% if search_query or filters.get('user_type') or filters.get('status') %}
                            Aucun utilisateur ne correspond à vos critères de recherche.
                        {% else %}
                            Aucun utilisateur dans la base de données.
                        {% endif %}
                    </p>
                </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- Modal Nouvel Utilisateur -->
<div class="modal fade" id="addUserModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>Nouvel Utilisateur
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addUserForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="new_first_name" class="form-label">Prénom *</label>
                            <input type="text" class="form-control" id="new_first_name" name="first_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="new_last_name" class="form-label">Nom *</label>
                            <input type="text" class="form-control" id="new_last_name" name="last_name" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="new_email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="new_email" name="email" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="new_username" class="form-label">Nom d'utilisateur *</label>
                            <input type="text" class="form-control" id="new_username" name="username" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="new_user_type" class="form-label">Type d'utilisateur *</label>
                            <select class="form-select" id="new_user_type" name="user_type" required>
                                <option value="">Sélectionnez</option>
                                <option value="transporteur">Transporteur</option>
                                <option value="expediteur">Expéditeur</option>
                                <option value="chauffeur">Chauffeur</option>
                                <option value="admin">Administrateur</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="new_phone" class="form-label">Téléphone</label>
                            <input type="tel" class="form-control" id="new_phone" name="phone">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="new_company_name" class="form-label">Entreprise</label>
                        <input type="text" class="form-control" id="new_company_name" name="company_name">
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="new_password" class="form-label">Mot de passe temporaire *</label>
                            <input type="password" class="form-control" id="new_password" name="password" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="new_confirm_password" class="form-label">Confirmer le mot de passe *</label>
                            <input type="password" class="form-control" id="new_confirm_password" name="confirm_password" required>
                        </div>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="new_is_active" name="is_active" checked>
                        <label class="form-check-label" for="new_is_active">
                            Compte actif
                        </label>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="new_is_verified" name="is_verified">
                        <label class="form-check-label" for="new_is_verified">
                            Compte vérifié
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>Créer l'utilisateur
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #003366, #90EE90);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 14px;
}

.user-card {
    transition: transform 0.2s;
}

.user-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.dashboard-card {
    border-left: 4px solid;
}

.dashboard-card.success {
    border-left-color: #28a745;
}

.dashboard-card.warning {
    border-left-color: #ffc107;
}

.dashboard-card.info {
    border-left-color: #17a2b8;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Changement de vue
document.addEventListener('DOMContentLoaded', function() {
    const tableView = document.getElementById('view-table');
    const cardsView = document.getElementById('view-cards');
    const tableContainer = document.getElementById('table-view');
    const cardsContainer = document.getElementById('cards-view');
    
    tableView.addEventListener('change', function() {
        if (this.checked) {
            tableContainer.style.display = 'block';
            cardsContainer.style.display = 'none';
        }
    });
    
    cardsView.addEventListener('change', function() {
        if (this.checked) {
            tableContainer.style.display = 'none';
            cardsContainer.style.display = 'block';
        }
    });
});

// Fonctions de gestion des utilisateurs
function viewUser(userId) {
    // Rediriger vers la page de détail de l'utilisateur
    window.location.href = `/admin/users/${userId}`;
}

function editUser(userId) {
    // Rediriger vers la page d'édition de l'utilisateur
    window.location.href = `/admin/users/${userId}/edit`;
}

function toggleUserStatus(userId, activate) {
    const action = activate ? 'activer' : 'désactiver';
    if (confirm(`Êtes-vous sûr de vouloir ${action} cet utilisateur ?`)) {
        fetch(`/admin/users/${userId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ active: activate })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification(`Utilisateur ${action} avec succès`, 'success');
                location.reload();
            } else {
                showNotification('Erreur lors de la modification', 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de la modification', 'error');
        });
    }
}

function verifyUser(userId) {
    if (confirm('Êtes-vous sûr de vouloir vérifier cet utilisateur ?')) {
        fetch(`/admin/users/${userId}/verify`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showNotification('Utilisateur vérifié avec succès', 'success');
                location.reload();
            } else {
                showNotification('Erreur lors de la vérification', 'error');
            }
        })
        .catch(error => {
            showNotification('Erreur lors de la vérification', 'error');
        });
    }
}

// Gestion du formulaire de création d'utilisateur
document.getElementById('addUserForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const password = formData.get('password');
    const confirmPassword = formData.get('confirm_password');
    
    if (password !== confirmPassword) {
        showNotification('Les mots de passe ne correspondent pas', 'error');
        return;
    }
    
    fetch('/admin/users/create', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('Utilisateur créé avec succès', 'success');
            bootstrap.Modal.getInstance(document.getElementById('addUserModal')).hide();
            location.reload();
        } else {
            showNotification(data.message || 'Erreur lors de la création', 'error');
        }
    })
    .catch(error => {
        showNotification('Erreur lors de la création', 'error');
    });
});

// Auto-submit des filtres
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const inputs = form.querySelectorAll('select, input[type="text"]');
    
    inputs.forEach(input => {
        input.addEventListener('change', function() {
            if (this.name !== 'search') {
                setTimeout(() => {
                    form.submit();
                }, 300);
            }
        });
    });
    
    // Pour la recherche, attendre un délai plus long
    const searchInput = document.getElementById('search');
    let searchTimeout;
    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            form.submit();
        }, 1000);
    });
});
</script>
{% endblock %}
