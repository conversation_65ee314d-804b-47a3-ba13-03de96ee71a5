#!/usr/bin/env python3
"""
Script de test pour vérifier le démarrage de l'application SABTRANS
"""

import sys
import os

# Ajouter le répertoire du projet au path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🔄 Test de démarrage de l'application SABTRANS...")
    
    # Test d'import des modules principaux
    print("📦 Test des imports...")
    
    try:
        from flask import Flask
        print("✅ Flask importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur import Flask: {e}")
        sys.exit(1)
    
    try:
        from database import db
        print("✅ Database importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur import Database: {e}")
        sys.exit(1)
    
    try:
        from models.user import User
        print("✅ Modèle User importé avec succès")
    except ImportError as e:
        print(f"❌ Erreur import User: {e}")
        sys.exit(1)
    
    try:
        from models.freight import FreightOffer, FreightProposal
        print("✅ Modèles Freight importés avec succès")
    except ImportError as e:
        print(f"❌ Erreur import Freight: {e}")
        sys.exit(1)
    
    # Test de création de l'application
    print("\n🏗️ Test de création de l'application...")
    
    try:
        from app import create_app
        app = create_app()
        print("✅ Application créée avec succès")
    except Exception as e:
        print(f"❌ Erreur création app: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    # Test du contexte de l'application
    print("\n🔧 Test du contexte de l'application...")
    
    try:
        with app.app_context():
            # Test de connexion à la base de données
            from sqlalchemy import text
            result = db.session.execute(text("SELECT 1"))
            print("✅ Connexion base de données OK")
            
            # Test de comptage des utilisateurs
            user_count = User.query.count()
            print(f"✅ Nombre d'utilisateurs: {user_count}")
            
            # Test de comptage des offres
            offer_count = FreightOffer.query.count()
            print(f"✅ Nombre d'offres: {offer_count}")
            
            # Test de comptage des propositions
            proposal_count = FreightProposal.query.count()
            print(f"✅ Nombre de propositions: {proposal_count}")
            
    except Exception as e:
        print(f"❌ Erreur contexte app: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    # Test des routes principales
    print("\n🌐 Test des routes principales...")
    
    try:
        with app.test_client() as client:
            # Test route principale
            response = client.get('/')
            print(f"✅ Route / : {response.status_code}")
            
            # Test route freight
            response = client.get('/freight/')
            print(f"✅ Route /freight/ : {response.status_code}")
            
            # Test route auth
            response = client.get('/auth/login')
            print(f"✅ Route /auth/login : {response.status_code}")
            
    except Exception as e:
        print(f"❌ Erreur test routes: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
    
    print("\n🎉 Tous les tests sont passés avec succès !")
    print("✅ L'application SABTRANS est prête à démarrer")
    
    # Test de démarrage en mode debug
    print("\n🚀 Test de démarrage en mode debug...")
    try:
        print("ℹ️ Démarrage de l'application sur http://localhost:5000")
        print("ℹ️ Appuyez sur Ctrl+C pour arrêter")
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 Application arrêtée par l'utilisateur")
    except Exception as e:
        print(f"❌ Erreur démarrage: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

except Exception as e:
    print(f"❌ Erreur générale: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
