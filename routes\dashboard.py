from flask import Blueprint, render_template, request, jsonify
from flask_login import login_required, current_user
from models.user import User
from models.freight import FreightOffer, FreightProposal
from models.mission import Mission
from models.document import Document
from app import db
from sqlalchemy import func, desc
from datetime import datetime, timedelta

dashboard_bp = Blueprint('dashboard', __name__)

@dashboard_bp.route('/')
@login_required
def index():
    """Dashboard principal selon le type d'utilisateur"""
    if current_user.is_admin():
        return admin_dashboard()
    elif current_user.is_transporter():
        return transporter_dashboard()
    elif current_user.is_shipper():
        return shipper_dashboard()
    elif current_user.is_driver():
        return driver_dashboard()
    else:
        return render_template('dashboard/default.html')

def admin_dashboard():
    """Dashboard administrateur"""
    # Statistiques générales
    total_users = User.query.count()
    total_offers = FreightOffer.query.count()
    total_missions = Mission.query.count()
    active_missions = Mission.query.filter(Mission.status.in_(['in_transit', 'pickup_ready'])).count()
    
    # Utilisateurs par type
    user_stats = db.session.query(
        User.user_type,
        func.count(User.id).label('count')
    ).group_by(User.user_type).all()
    
    # Missions par statut
    mission_stats = db.session.query(
        Mission.status,
        func.count(Mission.id).label('count')
    ).group_by(Mission.status).all()
    
    # Offres récentes
    recent_offers = FreightOffer.query.order_by(desc(FreightOffer.created_at)).limit(5).all()
    
    # Missions récentes
    recent_missions = Mission.query.order_by(desc(Mission.created_at)).limit(5).all()
    
    return render_template('dashboard/admin.html',
                         total_users=total_users,
                         total_offers=total_offers,
                         total_missions=total_missions,
                         active_missions=active_missions,
                         user_stats=user_stats,
                         mission_stats=mission_stats,
                         recent_offers=recent_offers,
                         recent_missions=recent_missions)

def transporter_dashboard():
    """Dashboard transporteur"""
    # Statistiques du transporteur
    my_missions = Mission.query.filter_by(transporter_id=current_user.id)
    total_missions = my_missions.count()
    active_missions = my_missions.filter(Mission.status.in_(['accepted', 'assigned', 'pickup_ready', 'in_transit'])).count()
    completed_missions = my_missions.filter_by(status='completed').count()
    
    # Revenus
    total_revenue = db.session.query(func.sum(Mission.agreed_price)).filter(
        Mission.transporter_id == current_user.id,
        Mission.status == 'completed'
    ).scalar() or 0
    
    # Missions en cours
    current_missions = my_missions.filter(
        Mission.status.in_(['accepted', 'assigned', 'pickup_ready', 'in_transit'])
    ).order_by(Mission.pickup_scheduled_at).all()
    
    # Nouvelles offres disponibles
    available_offers = FreightOffer.query.filter(
        FreightOffer.status == 'active',
        FreightOffer.offer_type == 'demande',
        FreightOffer.user_id != current_user.id
    ).order_by(desc(FreightOffer.created_at)).limit(10).all()
    
    # Missions récentes
    recent_missions = my_missions.order_by(desc(Mission.created_at)).limit(5).all()
    
    # Mes propositions envoyées
    my_proposals = FreightProposal.query.filter_by(transporter_id=current_user.id).order_by(
        FreightProposal.created_at.desc()
    ).limit(10).all()

    # Statistiques des propositions
    pending_proposals = FreightProposal.query.filter_by(
        transporter_id=current_user.id,
        status='pending'
    ).count()

    accepted_proposals = FreightProposal.query.filter_by(
        transporter_id=current_user.id,
        status='accepted'
    ).count()

    # Évaluations moyennes
    avg_rating = db.session.query(func.avg(Mission.transporter_rating)).filter(
        Mission.transporter_id == current_user.id,
        Mission.transporter_rating.isnot(None)
    ).scalar() or 0

    return render_template('dashboard/transporter.html',
                         total_missions=total_missions,
                         active_missions=active_missions,
                         completed_missions=completed_missions,
                         total_revenue=total_revenue,
                         current_missions=current_missions,
                         available_offers=available_offers,
                         recent_missions=recent_missions,
                         my_proposals=my_proposals,
                         pending_proposals=pending_proposals,
                         accepted_proposals=accepted_proposals,
                         avg_rating=round(avg_rating, 1) if avg_rating else 0)

def shipper_dashboard():
    """Dashboard expéditeur"""
    # Statistiques de l'expéditeur
    my_offers = FreightOffer.query.filter_by(user_id=current_user.id)
    total_offers = my_offers.count()
    active_offers = my_offers.filter_by(status='active').count()
    
    my_missions = Mission.query.filter_by(shipper_id=current_user.id)
    total_missions = my_missions.count()
    active_missions = my_missions.filter(Mission.status.in_(['accepted', 'assigned', 'pickup_ready', 'in_transit'])).count()
    completed_missions = my_missions.filter_by(status='completed').count()
    
    # Coûts
    total_cost = db.session.query(func.sum(Mission.agreed_price)).filter(
        Mission.shipper_id == current_user.id,
        Mission.status == 'completed'
    ).scalar() or 0
    
    # Missions en cours
    current_missions = my_missions.filter(
        Mission.status.in_(['accepted', 'assigned', 'pickup_ready', 'in_transit'])
    ).order_by(Mission.pickup_scheduled_at).all()
    
    # Mes offres actives
    my_active_offers = my_offers.filter_by(status='active').order_by(desc(FreightOffer.created_at)).limit(5).all()
    
    # Missions récentes
    recent_missions = my_missions.order_by(desc(Mission.created_at)).limit(5).all()
    
    # Propositions reçues pour mes demandes
    my_proposals = db.session.query(FreightProposal).join(FreightOffer).filter(
        FreightOffer.user_id == current_user.id,
        FreightOffer.offer_type == 'demande'
    ).order_by(FreightProposal.created_at.desc()).limit(10).all()

    # Statistiques des propositions
    pending_proposals = db.session.query(FreightProposal).join(FreightOffer).filter(
        FreightOffer.user_id == current_user.id,
        FreightProposal.status == 'pending'
    ).count()

    # Demandes avec propositions
    offers_with_proposals = db.session.query(
        FreightOffer.id,
        FreightOffer.title,
        FreightOffer.pickup_city,
        FreightOffer.delivery_city,
        func.count(FreightProposal.id).label('proposal_count'),
        func.min(FreightProposal.proposed_price).label('best_price')
    ).join(FreightProposal).filter(
        FreightOffer.user_id == current_user.id,
        FreightOffer.offer_type == 'demande',
        FreightProposal.status == 'pending'
    ).group_by(FreightOffer.id).order_by(func.count(FreightProposal.id).desc()).limit(5).all()

    # Évaluations moyennes
    avg_rating = db.session.query(func.avg(Mission.shipper_rating)).filter(
        Mission.shipper_id == current_user.id,
        Mission.shipper_rating.isnot(None)
    ).scalar() or 0

    return render_template('dashboard/shipper.html',
                         total_offers=total_offers,
                         active_offers=active_offers,
                         total_missions=total_missions,
                         active_missions=active_missions,
                         completed_missions=completed_missions,
                         total_cost=total_cost,
                         current_missions=current_missions,
                         my_active_offers=my_active_offers,
                         recent_missions=recent_missions,
                         my_proposals=my_proposals,
                         pending_proposals=pending_proposals,
                         offers_with_proposals=offers_with_proposals,
                         avg_rating=round(avg_rating, 1) if avg_rating else 0)

def driver_dashboard():
    """Dashboard chauffeur"""
    # Missions assignées au chauffeur
    my_missions = Mission.query.filter_by(driver_id=current_user.id)
    total_missions = my_missions.count()
    active_missions = my_missions.filter(Mission.status.in_(['assigned', 'pickup_ready', 'in_transit'])).count()
    completed_missions = my_missions.filter_by(status='completed').count()
    
    # Mission actuelle
    current_mission = my_missions.filter(
        Mission.status.in_(['assigned', 'pickup_ready', 'in_transit'])
    ).first()
    
    # Missions du jour
    today = datetime.utcnow().date()
    today_missions = my_missions.filter(
        func.date(Mission.pickup_scheduled_at) == today
    ).order_by(Mission.pickup_scheduled_at).all()
    
    # Missions de la semaine
    week_start = today - timedelta(days=today.weekday())
    week_end = week_start + timedelta(days=6)
    week_missions = my_missions.filter(
        func.date(Mission.pickup_scheduled_at).between(week_start, week_end)
    ).order_by(Mission.pickup_scheduled_at).all()
    
    # Missions récentes
    recent_missions = my_missions.order_by(desc(Mission.created_at)).limit(5).all()
    
    return render_template('dashboard/driver.html',
                         total_missions=total_missions,
                         active_missions=active_missions,
                         completed_missions=completed_missions,
                         current_mission=current_mission,
                         today_missions=today_missions,
                         week_missions=week_missions,
                         recent_missions=recent_missions)

@dashboard_bp.route('/stats/api')
@login_required
def stats_api():
    """API pour les statistiques du dashboard"""
    if current_user.is_admin():
        # Statistiques pour les 30 derniers jours
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        
        # Nouvelles inscriptions par jour
        registrations = db.session.query(
            func.date(User.created_at).label('date'),
            func.count(User.id).label('count')
        ).filter(User.created_at >= thirty_days_ago).group_by(func.date(User.created_at)).all()
        
        # Nouvelles offres par jour
        offers = db.session.query(
            func.date(FreightOffer.created_at).label('date'),
            func.count(FreightOffer.id).label('count')
        ).filter(FreightOffer.created_at >= thirty_days_ago).group_by(func.date(FreightOffer.created_at)).all()
        
        # Missions créées par jour
        missions = db.session.query(
            func.date(Mission.created_at).label('date'),
            func.count(Mission.id).label('count')
        ).filter(Mission.created_at >= thirty_days_ago).group_by(func.date(Mission.created_at)).all()
        
        return jsonify({
            'registrations': [{'date': str(r.date), 'count': r.count} for r in registrations],
            'offers': [{'date': str(o.date), 'count': o.count} for o in offers],
            'missions': [{'date': str(m.date), 'count': m.count} for m in missions]
        })
    
    return jsonify({'error': 'Accès non autorisé'}), 403

@dashboard_bp.route('/notifications')
@login_required
def notifications():
    """Page des notifications"""
    # Ici on pourrait implémenter un système de notifications
    # Pour l'instant, on retourne une page simple
    notifications = []
    
    if current_user.is_transporter():
        # Nouvelles offres disponibles
        new_offers_count = FreightOffer.query.filter(
            FreightOffer.status == 'active',
            FreightOffer.offer_type == 'demande',
            FreightOffer.user_id != current_user.id,
            FreightOffer.created_at >= datetime.utcnow() - timedelta(hours=24)
        ).count()
        
        if new_offers_count > 0:
            notifications.append({
                'type': 'info',
                'title': 'Nouvelles offres disponibles',
                'message': f'{new_offers_count} nouvelle(s) offre(s) de transport disponible(s)',
                'url': '/freight/search'
            })
    
    elif current_user.is_shipper():
        # Missions en retard
        overdue_missions = Mission.query.filter(
            Mission.shipper_id == current_user.id,
            Mission.delivery_scheduled_at < datetime.utcnow(),
            Mission.status.in_(['accepted', 'assigned', 'pickup_ready', 'in_transit'])
        ).count()
        
        if overdue_missions > 0:
            notifications.append({
                'type': 'warning',
                'title': 'Missions en retard',
                'message': f'{overdue_missions} mission(s) en retard de livraison',
                'url': '/dashboard'
            })
    
    return render_template('dashboard/notifications.html', notifications=notifications)
