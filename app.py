from flask import Flask, render_template, redirect, url_for, flash, request
from flask_login import <PERSON>gin<PERSON>ana<PERSON>, login_required, current_user
from config import config
from database import db, migrate
import os

# Initialisation des extensions
login_manager = LoginManager()

def create_app(config_name=None):
    if config_name is None:
        config_name = os.environ.get('FLASK_ENV', 'development')
    
    app = Flask(__name__)
    app.config.from_object(config[config_name])
    
    # Initialisation des extensions
    db.init_app(app)
    login_manager.init_app(app)
    migrate.init_app(app, db)
    
    # Configuration du login manager
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'Veuillez vous connecter pour accéder à cette page.'
    login_manager.login_message_category = 'info'
    
    # Import des modèles
    from models.user import User
    from models.freight import FreightOffer
    from models.document import Document
    from models.mission import Mission
    
    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))
    
    # Import et enregistrement des blueprints
    from routes.auth import auth_bp
    from routes.dashboard import dashboard_bp
    from routes.freight import freight_bp
    from routes.documents import documents_bp
    from routes.admin import admin_bp

    app.register_blueprint(auth_bp, url_prefix='/auth')
    app.register_blueprint(dashboard_bp, url_prefix='/dashboard')
    app.register_blueprint(freight_bp, url_prefix='/freight')
    app.register_blueprint(documents_bp, url_prefix='/documents')
    app.register_blueprint(admin_bp)
    
    # Route principale
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard.index'))
        return render_template('index.html')
    
    # Gestion des erreurs
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500
    
    # Création des dossiers nécessaires
    os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
    
    # Filtres Jinja2 personnalisés
    @app.template_filter('safe_strftime')
    def safe_strftime(date, format='%d/%m/%Y'):
        """Filtre pour formater les dates de manière sécurisée"""
        if date is None:
            return 'Date non définie'
        try:
            return date.strftime(format)
        except (AttributeError, ValueError):
            return 'Date invalide'

    @app.template_filter('safe_datetime')
    def safe_datetime(date, format='%d/%m/%Y à %H:%M'):
        """Filtre pour formater les dates avec heure de manière sécurisée"""
        if date is None:
            return 'Date non définie'
        try:
            return date.strftime(format)
        except (AttributeError, ValueError):
            return 'Date invalide'

    return app

if __name__ == '__main__':
    app = create_app()
    try:
        with app.app_context():
            db.create_all()
        print("✅ Base de données initialisée avec succès")
    except Exception as e:
        print(f"⚠️  Erreur de base de données: {e}")
        print("💡 Assurez-vous que MySQL est démarré et que la base de données 'sabtrans' existe")
        print("   Vous pouvez créer la base avec: CREATE DATABASE sabtrans;")

    print("🚀 Démarrage de SABTRANS sur http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
