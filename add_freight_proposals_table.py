#!/usr/bin/env python3
"""
Script pour ajouter la table freight_proposals à la base de données
"""

import pymysql
from config import config
import os

def create_freight_proposals_table():
    """Crée la table freight_proposals"""
    
    # Configuration par défaut
    user = 'root'
    password = ''
    host = 'localhost'
    port = 3306
    database = 'sabtrans'
    
    print(f"🔗 Connexion à la base de données...")
    print(f"   Host: {host}:{port}")
    print(f"   Database: {database}")
    print(f"   User: {user}")
    
    try:
        # Connexion à la base de données
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # Vérifier si la table existe déjà
            cursor.execute("""
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = %s 
                AND TABLE_NAME = 'freight_proposals'
            """, (database,))
            
            table_exists = cursor.fetchone()[0] > 0
            
            if table_exists:
                print("✅ La table 'freight_proposals' existe déjà")
                return True
            
            print("📝 Création de la table 'freight_proposals'...")
            
            # Créer la table freight_proposals
            cursor.execute("""
                CREATE TABLE freight_proposals (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    freight_offer_id INT NOT NULL,
                    transporter_id INT NOT NULL,
                    proposed_price DECIMAL(10,2) NOT NULL,
                    price_type ENUM('fixe', 'negociable', 'au_km') DEFAULT 'fixe',
                    currency VARCHAR(3) DEFAULT 'EUR',
                    message TEXT,
                    estimated_duration INT,
                    vehicle_details TEXT,
                    status ENUM('pending', 'accepted', 'rejected', 'withdrawn') DEFAULT 'pending',
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    responded_at DATETIME NULL,
                    
                    FOREIGN KEY (freight_offer_id) REFERENCES freight_offers(id) ON DELETE CASCADE,
                    FOREIGN KEY (transporter_id) REFERENCES users(id) ON DELETE CASCADE,
                    
                    INDEX idx_freight_offer (freight_offer_id),
                    INDEX idx_transporter (transporter_id),
                    INDEX idx_status (status),
                    INDEX idx_price (proposed_price),
                    INDEX idx_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                COMMENT='Propositions de prix des transporteurs pour les demandes de fret'
            """)
            
            # Valider les changements
            connection.commit()
            
            print("✅ Table 'freight_proposals' créée avec succès !")
            
            # Vérifier que la table a bien été créée
            cursor.execute("""
                SELECT COUNT(*) 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_SCHEMA = %s 
                AND TABLE_NAME = 'freight_proposals'
            """, (database,))
            
            if cursor.fetchone()[0] > 0:
                print("✅ Vérification : La table a bien été créée")
                
                # Afficher la structure de la table
                cursor.execute("DESCRIBE freight_proposals")
                columns = cursor.fetchall()
                
                print("\n📋 Structure de la table 'freight_proposals' :")
                for column in columns:
                    field, type_info, null, key, default, extra = column
                    print(f"   - {field}: {type_info} {'(NULL)' if null == 'YES' else '(NOT NULL)'}")
                
                return True
            else:
                print("❌ Erreur : La table n'a pas été créée")
                return False
                
    except pymysql.Error as e:
        print(f"❌ Erreur MySQL : {e}")
        return False
    except Exception as e:
        print(f"❌ Erreur : {e}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()
            print("🔌 Connexion fermée")

def add_sample_proposals():
    """Ajoute quelques propositions d'exemple"""
    
    user = 'root'
    password = ''
    host = 'localhost'
    port = 3306
    database = 'sabtrans'
    
    try:
        connection = pymysql.connect(
            host=host,
            port=port,
            user=user,
            password=password,
            database=database,
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            print("\n📝 Ajout de propositions d'exemple...")
            
            # Vérifier s'il y a des offres de type 'demande'
            cursor.execute("SELECT id FROM freight_offers WHERE offer_type = 'demande' LIMIT 3")
            offers = cursor.fetchall()
            
            if not offers:
                print("ℹ️ Aucune demande de fret trouvée pour ajouter des propositions")
                return True
            
            # Vérifier s'il y a des transporteurs
            cursor.execute("SELECT id FROM users WHERE user_type = 'transporteur' LIMIT 3")
            transporters = cursor.fetchall()
            
            if not transporters:
                print("ℹ️ Aucun transporteur trouvé pour ajouter des propositions")
                return True
            
            # Ajouter des propositions d'exemple
            sample_proposals = [
                {
                    'freight_offer_id': offers[0][0],
                    'transporter_id': transporters[0][0] if len(transporters) > 0 else 1,
                    'proposed_price': 850.00,
                    'message': 'Transport rapide et sécurisé avec véhicule frigorifique',
                    'estimated_duration': 8,
                    'vehicle_details': 'Camion frigorifique 20m³, hayon élévateur'
                },
                {
                    'freight_offer_id': offers[0][0],
                    'transporter_id': transporters[1][0] if len(transporters) > 1 else 1,
                    'proposed_price': 780.00,
                    'message': 'Meilleur prix garanti, expérience 15 ans',
                    'estimated_duration': 10,
                    'vehicle_details': 'Semi-remorque bâchée 33 palettes'
                },
                {
                    'freight_offer_id': offers[0][0],
                    'transporter_id': transporters[2][0] if len(transporters) > 2 else 1,
                    'proposed_price': 920.00,
                    'message': 'Service premium avec suivi GPS temps réel',
                    'estimated_duration': 6,
                    'vehicle_details': 'Camion plateau avec grue embarquée'
                }
            ]
            
            for proposal in sample_proposals:
                cursor.execute("""
                    INSERT INTO freight_proposals 
                    (freight_offer_id, transporter_id, proposed_price, message, estimated_duration, vehicle_details)
                    VALUES (%(freight_offer_id)s, %(transporter_id)s, %(proposed_price)s, 
                           %(message)s, %(estimated_duration)s, %(vehicle_details)s)
                """, proposal)
            
            connection.commit()
            print(f"✅ {len(sample_proposals)} propositions d'exemple ajoutées")
            
            return True
            
    except Exception as e:
        print(f"❌ Erreur lors de l'ajout des propositions : {e}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == '__main__':
    print("🚀 Script de création de la table freight_proposals")
    print("=" * 60)
    
    # Créer la table
    success = create_freight_proposals_table()
    
    if success:
        # Ajouter des données d'exemple
        add_sample_proposals()
        
        print("\n🎉 Migration terminée avec succès !")
        print("   La table 'freight_proposals' est maintenant disponible")
        print("   Les transporteurs peuvent maintenant proposer des prix")
        print("   Vous pouvez redémarrer l'application SABTRANS")
    else:
        print("\n❌ Échec de la migration")
        print("   Vérifiez les paramètres de connexion à la base de données")
    
    print("=" * 60)
